package com.hmdp.utils;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hmdp.entity.Shop;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.print.attribute.standard.JobSheets;
import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.hmdp.utils.RedisConstants.CACHE_SHOP_KEY;

/**
 * @return
 */

@Component
@Slf4j
public class CacheClient {

    private StringRedisTemplate stringRedisTemplate;
    public CacheClient(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }
    //* 方法1：将任意Java对象序列化为json并存储在string类型的key中，并且可以设置TTL过期时间
    public void set(String key, Object value, Long time, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(value), time, unit);
    }
    //* 方法2：将任意Java对象序列化为json并存储在string类型的key中，并且可以设置逻辑过期时间，用于处理缓存击穿问题

    public void setWithoutTTL(String key, Object value, Long time, TimeUnit unit) {
        RedisData redisData = new RedisData();
        redisData.setData(value);
        redisData.setExpireTime(LocalDateTime.now().plusSeconds(unit.toSeconds(time)));
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(redisData), time, unit);
    }
    //* 方法3：根据指定的key查询缓存，并反序列化为指定类型，利用缓存空值的方式解决缓存穿透问题
    public <R,ID> R queryWithPassThrough(
            String keyPrefix, ID id, Class<R> type, Function<ID, R> dbFallback, Long time, TimeUnit unit){
        String key = keyPrefix + id;
        // 1.从redis查询商铺缓存
        String json = stringRedisTemplate.opsForValue().get(key);
        // 2.判断是否存在
        if (StrUtil.isNotBlank(json)) {
            // 3.存在，直接返回
            return JSONUtil.toBean(json, type);
        }
        // 判断命中的是否是空值
        if (json != null) {
            // 返回一个错误信息
            return null;
        }

        // 4.不存在，根据id查询数据库
        R r = dbFallback.apply(id);
        // 5.不存在，返回错误
        if (r == null) {
            // 将空值写入redis
            stringRedisTemplate.opsForValue().set(key, "", time, unit);
            // 返回错误信息
            return null;
        }
        // 6.存在，写入redis
        this.set(key, r, time, unit);
        return r;
    }
    //* 方法4：根据指定的key查询缓存，并反序列化为指定类型，需要利用逻辑过期解决缓存击穿问题
    private static final ExecutorService CACHE_REBUILD_EXECUTOR = Executors.newFixedThreadPool(10);
    public <R, ID> R queryWithLogicalExpire(String strKey, ID id, Class<R> type, Long time, TimeUnit unit, Function<ID, R> dbFallback ) {
        String key = strKey + id;
        //查询缓存检查逻辑时间（特定的场景下，查询的是热点数据，已经放到redis
        String redisdata = stringRedisTemplate.opsForValue().get(key);
        if(StrUtil.isBlank(redisdata)) {
            return null;
        }
        com.hmdp.entity.RedisData redisData = JSONUtil.toBean(redisdata, com.hmdp.entity.RedisData.class);
        JSONObject data = (JSONObject)redisData.getData();
        R rold = JSONUtil.toBean(data, type);
        LocalDateTime expireTime1 = redisData.getExpireTime();
        //未过期，直接返回
        if(expireTime1.isAfter(LocalDateTime.now())) {
            return rold;
        }
        //过期，尝试获取锁
        boolean flag = tryLock("Lock:" + key);
        //成功获取锁，
        if(BooleanUtil.isTrue(flag)) {
            String s = stringRedisTemplate.opsForValue().get(key);
            R r1 = JSONUtil.toBean((JSONObject) JSONUtil.toBean(s, com.hmdp.entity.RedisData.class).getData(), type);
            LocalDateTime time1 = JSONUtil.toBean(s, com.hmdp.entity.RedisData.class).getExpireTime();
            if(time1.isAfter(LocalDateTime.now())) {
                return r1;
            }
            //，创建一个线程，执行缓存重建，更新逻辑过期时间，释放锁，当前线程返回过期数据
            CACHE_REBUILD_EXECUTOR.submit( ()->{

                try{
                    //重建缓存
                    this.saveShopRedis(strKey, id, time, unit, type, dbFallback);
                }catch (Exception e){
                    throw new RuntimeException(e);
                }finally {
                    dropLock("Lock:" + key);
                }
            });
            /*CACHE_REBUILD_EXECUTOR.submit(() -> {
                try {
                    // 查询数据库
                    R newR = dbFallback.apply(id);
                    // 重建缓存
                    this.saveShopRedis(key, newR, time, unit);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }finally {
                    // 释放锁
                    dropLock("Lock:" + key);
                }
            });*/
        }
        //获取锁失败，返回过期数据
        return rold;
    }
    /*public <R>void saveShopRedis(String strkey, R r, Long expireTime, TimeUnit unit) {

        com.hmdp.entity.RedisData data = new com.hmdp.entity.RedisData();
        data.setData(r);
        data.setExpireTime(LocalDateTime.now().plusSeconds(unit.toSeconds(unit.toSeconds(expireTime))));
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(data));
    }*/
    public <R, ID> void saveShopRedis(String strKey, ID id, Long time, TimeUnit unit, Class<R> type, Function<ID, R>db ) {
        R r = db.apply(id);
        com.hmdp.entity.RedisData data = new com.hmdp.entity.RedisData();

        data.setData(r);
        data.setExpireTime(LocalDateTime.now().plusSeconds(unit.toSeconds(time)));
        stringRedisTemplate.opsForValue().set(strKey + id, JSONUtil.toJsonStr(data));
    }
    private boolean tryLock(String key) {
        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", 30, TimeUnit.SECONDS);
        return BooleanUtil.isTrue(b);
    }
    private void dropLock(String key) {
        stringRedisTemplate.delete(key);
    }
}
