package com.hmdp.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hmdp.dto.Result;
import com.hmdp.entity.ShopType;
import com.hmdp.mapper.ShopTypeMapper;
import com.hmdp.service.IShopTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.utils.RedisConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
public class ShopTypeServiceImpl extends ServiceImpl<ShopTypeMapper, ShopType> implements IShopTypeService {


    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private IShopTypeService typeService;

    @Override
    public Result queryShopTypeList() {
       /* //先去缓存查
        String key = RedisConstants.SHOP_TYPE_KEY;
        String s = stringRedisTemplate.opsForValue().get(key);
        if(StrUtil.isNotBlank(s)) {
            List<ShopType> shopTypes = JSONUtil.toList(JSONUtil.parseArray(s), ShopType.class);
            return Result.ok(shopTypes);
        }
        //如果没有，则去数据库查并存入redis
        List<ShopType> typeList = this
                .query().orderByAsc("sort").list();
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(typeList));
        return Result.ok(typeList);*/

        //使用list数据结构
        //先去缓存查
        String  key = RedisConstants.SHOP_TYPE_KEY;
        Long size = stringRedisTemplate.opsForList().size(key);
        List<String> shopJson = stringRedisTemplate.opsForList().range(key, 0, size - 1);
        if(!(shopJson == null || shopJson.isEmpty())) {
            ArrayList<ShopType> shopTypes = new ArrayList<>();
            for (String s : shopJson) {
                shopTypes.add(JSONUtil.toBean(s, ShopType.class));
            }
            return Result.ok(shopTypes);
        }
        List<ShopType> typeList = this
                .query().orderByAsc("sort").list();
        for (ShopType shopType : typeList) {
            stringRedisTemplate.opsForList().rightPush(key, JSONUtil.toJsonStr(shopType));
        }
        /*stringRedisTemplate.opsForList().leftPush(key, JSONUtil.toJsonStr(typeList));*/
        return Result.ok(typeList);

    }
}
