package com.hmdp.utils;

import cn.hutool.core.bean.BeanUtil;
import com.hmdp.dto.UserDTO;
import com.hmdp.entity.User;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.baomidou.mybatisplus.core.toolkit.Wrappers.query;

/**
 * @return
 */
//自定义拦截器
public class LoginIntercepter implements HandlerInterceptor {


    /*private StringRedisTemplate stringRedisTemplate;
    public LoginIntercepter(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }*/

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
       /* HttpSession session = request.getSession();//获取session
        UserDTO user = (UserDTO) session.getAttribute("user");//获取session中的用户信息
        if(user == null) {//校验
            response.setStatus(401);
            return false;
        }
        UserHolder.saveUser(user);
        return true;*/

        //从threadlocal中取数据查用户信息做登录校验
        UserDTO user = UserHolder.getUser();
        if (UserHolder.getUser() == null) {
            // 没有，需要拦截，设置状态码
            response.setStatus(401);
            // 拦截
            return false;
        }
        // 有用户，则放行
        return true;
    }

    /*@Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserHolder.removeUser();
    }*/
}
