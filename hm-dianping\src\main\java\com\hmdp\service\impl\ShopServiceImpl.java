package com.hmdp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hmdp.dto.Result;
import com.hmdp.entity.RedisData;
import com.hmdp.entity.Shop;
import com.hmdp.entity.User;
import com.hmdp.mapper.ShopMapper;
import com.hmdp.service.IShopService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.utils.CacheClient;
import com.hmdp.utils.RedisConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.hmdp.utils.RedisConstants.CACHE_SHOP_KEY;
import static com.hmdp.utils.RedisConstants.CACHE_SHOP_TTL;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements IShopService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CacheClient cacheClient;

    @Override
    public Result queryById(Long id) {
        //解决缓存穿透
        //Shop shop = queryWithPassThrough(id);

        //使用互斥锁解决缓存击穿，好像也解决了缓存穿透
        //Shop shop = queryWithMutex(id);

        //使用逻辑过期解决缓存击穿
        //Shop shop = queryWithLogicalExpire(id, 1L);
        Shop shop = cacheClient.queryWithLogicalExpire(CACHE_SHOP_KEY, id, Shop.class, CACHE_SHOP_TTL, TimeUnit.MINUTES, this::getById);
        if(shop == null) {
            return Result.fail("店铺不存在");
        }
        return Result.ok(shop);
    }


    /**
     * 缓存预热,将热点数据及逻辑过期时间放如缓存
     * @param id
     * @param expireTime
     */
    public void saveShopRedis(Long id, Long expireTime) {
        RedisData data = new RedisData();
        Shop shop = getById(id);
        data.setData(shop);
        data.setExpireTime(LocalDateTime.now().plusSeconds(expireTime));
        stringRedisTemplate.opsForValue().set(CACHE_SHOP_KEY + id, JSONUtil.toJsonStr(data));
    }

    /**
     * 用逻辑过期解决缓存击穿
     */
    private static final ExecutorService CACHE_REBUILD_EXECUTOR = Executors.newFixedThreadPool(10);
    public Shop queryWithLogicalExpire(Long id, Long expireTime) {
        String key = CACHE_SHOP_KEY + id;
        //查询缓存检查逻辑时间（特定的场景下，查询的是热点数据，已经放到redis
        String LogicalShop = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isBlank(LogicalShop)) {
            return null;
        }
        RedisData redisData = JSONUtil.toBean(LogicalShop, RedisData.class);
        JSONObject data = (JSONObject)redisData.getData();
        Shop shop = JSONUtil.toBean(data, Shop.class);
        LocalDateTime expireTime1 = redisData.getExpireTime();
        //未过期，直接返回
        if(expireTime1.isAfter(LocalDateTime.now())) {
            return shop;
        }
        //过期，尝试获取锁
        boolean flag = tryLock(RedisConstants.LOCK_SHOP_KEY + id);
        //成功获取锁，
        if(BooleanUtil.isTrue(flag)) {
            String s = stringRedisTemplate.opsForValue().get(key);
            Shop shop1 = JSONUtil.toBean((JSONObject) JSONUtil.toBean(s, RedisData.class).getData(), Shop.class);
            LocalDateTime time = JSONUtil.toBean(s, RedisData.class).getExpireTime();
            if(time.isAfter(LocalDateTime.now())) {
                return shop1;
            }
            //，创建一个线程，执行缓存重建，更新逻辑过期时间，释放锁，当前线程返回过期数据
            CACHE_REBUILD_EXECUTOR.submit( ()->{

                try{
                    //重建缓存
                    this.saveShopRedis(id,20L);
                }catch (Exception e){
                    throw new RuntimeException(e);
                }finally {
                    dropLock(RedisConstants.LOCK_SHOP_KEY + id);
                }
            });
        }
        //获取锁失败，返回过期数据
        return shop;
    }

    /**
     * 利用互斥锁解决缓存击穿
     * @param id
     * @return
     */
    public Shop queryWithMutex(Long id) {
        //先去redis查寻
        String key = CACHE_SHOP_KEY + id;
        String shopJson = stringRedisTemplate.opsForValue().get(key);//三种情况： 有值,null,""
        //isNOtBlank方法只有字符串中有值才会返回true
        if(StrUtil.isNotBlank(shopJson)) {
            return JSONUtil.toBean(shopJson, Shop.class);
        }
        //存在，判断值是否为null
        if("".equals(shopJson)) {
            //是缓存穿透数据,结束
            return null;
        }
        //不存在，则根据id查询数据库
        String lockKey = "lock:shop:" + id;
        Shop shop = null;
        try {
            boolean flag = tryLock(lockKey);
            if(!flag) {
                Thread.sleep(50);
                return queryWithMutex(id);
            }
            String shopJson1 = stringRedisTemplate.opsForValue().get(key);//三种情况： 有值,null,""
            //isNOtBlank方法只有字符串中有值才会返回true
            if(StrUtil.isNotBlank(shopJson1)) {
                return JSONUtil.toBean(shopJson, Shop.class);
            }
            shop = getById(id);
            //模拟重建延时
            Thread.sleep(200);
            if(shop == null) {
                stringRedisTemplate.opsForHash().put(key, "", RedisConstants.CACHE_NULL_TTL);
                return null;

            }
            stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(shop), CACHE_SHOP_TTL, TimeUnit.MINUTES);

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            dropLock(lockKey);
        }
        return shop;
    }
    private boolean tryLock(String key) {
        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", 30, TimeUnit.SECONDS);
        return BooleanUtil.isTrue(b);
    }
    private void dropLock(String key) {
        stringRedisTemplate.delete(key);
    }

    /**
     * 缓存穿透问题解决
     * @param id
     * @return
     */
    public Shop queryWithPassThrough(Long id) {
        //先去redis查寻
        String key = CACHE_SHOP_KEY + id;
        String shopJson = stringRedisTemplate.opsForValue().get(key);//三种情况： 有值,null,""
        //isNOtBlank方法只有字符串中有值才会返回true
        if(StrUtil.isNotBlank(shopJson)) {

            Shop shop = JSONUtil.toBean(shopJson, Shop.class);
            return shop;
        }
        //存在，判断值是否为null
        if("".equals(shopJson)) {
            //是缓存穿透数据,结束
            return null;
        }
        //不存在，则根据id查询数据库
        Shop shop = getById(id);
        if(shop == null) {
            stringRedisTemplate.opsForHash().put(key, "", RedisConstants.CACHE_NULL_TTL);
            return null;

        }
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(shop), CACHE_SHOP_TTL, TimeUnit.MINUTES);
        return shop;
    }
    @Override
    @Transactional
    public Result updateShop(Shop shop) {
        /**
         * 缓存更新策略
         * 限制性操作耗时长的，避免被其他线程趁虚而入
         *
         */
        if(shop.getId() == null) {
            return Result.fail("店铺不存在");
        }
        //先更新数据库，在删除缓存
        updateById(shop);
        stringRedisTemplate.delete(CACHE_SHOP_KEY + shop.getId());
        return Result.ok();

    }
}
