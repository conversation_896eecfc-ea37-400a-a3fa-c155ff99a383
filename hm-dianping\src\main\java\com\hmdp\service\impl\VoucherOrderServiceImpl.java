package com.hmdp.service.impl;

import com.hmdp.dto.Result;
import com.hmdp.entity.SeckillVoucher;
import com.hmdp.entity.VoucherOrder;
import com.hmdp.mapper.VoucherOrderMapper;
import com.hmdp.service.IVoucherOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.utils.RedisConstants;
import com.hmdp.utils.RedisIdWorker;
import com.hmdp.utils.SimpleRedislock;
import com.hmdp.utils.UserHolder;
import com.zaxxer.hikari.util.SuspendResumeLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
@Slf4j
public class VoucherOrderServiceImpl extends ServiceImpl<VoucherOrderMapper, VoucherOrder> implements IVoucherOrderService {


    @Autowired
    private SeckillVoucherServiceImpl seckillVoucherService;
    @Autowired
    private RedisIdWorker redisIdWorker;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /*@Override
    public Result seckkillVoucher(Long voucherId) {
        //查询优惠卷id返回优惠卷信息
        SeckillVoucher voucher = seckillVoucherService.getById(voucherId);
        log.info("优惠卷信息：{}", voucher);
        //判断秒杀是否开始
        if (LocalDateTime.now().isBefore(voucher.getBeginTime())) {
            //未开始直接返回异常信息
            return Result.fail("秒杀尚未开始");
        }
        if (LocalDateTime.now().isAfter(voucher.getEndTime())) {
            return Result.fail("秒杀已经结束！");
        }
        //判断库存是否充足
        if (voucher.getStock() <= 0) {
            //不充足返回异常信息
            return Result.fail("库存不足");
        }

        //Spring 5+ 的扩展方案：通过 AopContext 强制走代理
        //必须配置 @EnableAspectJAutoProxy(exposeProxy = true)
        //有侵入性，不推荐用于业务逻辑层，适合极少量特殊场景使用（如：事务嵌套逻辑封装）。

        Long userId = UserHolder.getUser().getId();
        *//*synchronized (userId.toString().intern()) {
            //获取代理对象
            IVoucherOrderService o = (IVoucherOrderService) AopContext.currentProxy();
            return o.ceratOreder(voucher);
        }*//*
        //当前方案无法解决集群模式下多个jvm，导致锁监视器不是同一个从而产生线程安全问题
        //改用分布式锁
        SimpleRedislock simpleRedislock = new SimpleRedislock(stringRedisTemplate, "VoucherOrder");
        boolean isLock = simpleRedislock.tryLock(120000);
        if(!isLock) {
            return Result.fail("不允许重复下单");
        }
        try {
            IVoucherOrderService o = (IVoucherOrderService) AopContext.currentProxy();
            return o.ceratOreder(voucher);
        } finally {
            simpleRedislock.unlock();
        }

    }

    @Transactional
    public Result ceratOreder(SeckillVoucher voucher) {
        //生成订单， 包括订单id， 优惠卷id, 用户id
        VoucherOrder voucherOrder = new VoucherOrder();
        Long orderId = redisIdWorker.nextId("voucherOder");
        voucherOrder.setId(orderId);
        Long voucherVoucherId = voucher.getVoucherId();
        voucherOrder.setVoucherId(voucherVoucherId);
        Long userId = UserHolder.getUser().getId();
        voucherOrder.setUserId(userId);
        //一人一单
        Integer count = query().eq("user_id", userId).eq("voucher_id", voucherVoucherId).count();
        if(count > 0) {
            return Result.fail("用户已购买过一次");
        }
        //充足则扣减库存并创建订单返回订单id
        boolean success = seckillVoucherService.update()
                .setSql("stock = stock - 1")
                .eq("voucher_id", voucherVoucherId)
                .gt("stock", 0)
                .update();
        if (!success) {
            //扣减库存
            return Result.fail("库存不足！");
        }
        save(voucherOrder);
        return Result.ok(orderId);

    }*/

    @Override
    public Result seckkillVoucher(Long voucherId) {
        /*//查询优惠卷id返回优惠卷信息
        SeckillVoucher voucher = seckillVoucherService.getById(voucherId);
        log.info("优惠卷信息：{}", voucher);
        //判断秒杀是否开始
        if (LocalDateTime.now().isBefore(voucher.getBeginTime())) {
            //未开始直接返回异常信息
            return Result.fail("秒杀尚未开始");
        }
        if (LocalDateTime.now().isAfter(voucher.getEndTime())) {
            return Result.fail("秒杀已经结束！");
        }*/
        //判断库存是否充足
        String stock = stringRedisTemplate.opsForValue().get(RedisConstants.SECKILL_STOCK_KEY + voucherId);
        if (stock ) {
            //不充足返回异常信息
            return Result.fail("库存不足");
        }

        //Spring 5+ 的扩展方案：通过 AopContext 强制走代理
        //必须配置 @EnableAspectJAutoProxy(exposeProxy = true)
        //有侵入性，不推荐用于业务逻辑层，适合极少量特殊场景使用（如：事务嵌套逻辑封装）。

        Long userId = UserHolder.getUser().getId();
        /*synchronized (userId.toString().intern()) {
            //获取代理对象
            IVoucherOrderService o = (IVoucherOrderService) AopContext.currentProxy();
            return o.ceratOreder(voucher);
        }*/
        //当前方案无法解决集群模式下多个jvm，导致锁监视器不是同一个从而产生线程安全问题
        //改用分布式锁
        SimpleRedislock simpleRedislock = new SimpleRedislock(stringRedisTemplate, "VoucherOrder");
        boolean isLock = simpleRedislock.tryLock(120000);
        if(!isLock) {
            return Result.fail("不允许重复下单");
        }
        try {
            IVoucherOrderService o = (IVoucherOrderService) AopContext.currentProxy();
            return o.ceratOreder(voucher);
        } finally {
            simpleRedislock.unlock();
        }

    }

    @Transactional
    public Result ceratOreder(SeckillVoucher voucher) {
        //生成订单， 包括订单id， 优惠卷id, 用户id
        VoucherOrder voucherOrder = new VoucherOrder();
        Long orderId = redisIdWorker.nextId("voucherOder");
        voucherOrder.setId(orderId);
        Long voucherVoucherId = voucher.getVoucherId();
        voucherOrder.setVoucherId(voucherVoucherId);
        Long userId = UserHolder.getUser().getId();
        voucherOrder.setUserId(userId);
        //一人一单
        Integer count = query().eq("user_id", userId).eq("voucher_id", voucherVoucherId).count();
        if(count > 0) {
            return Result.fail("用户已购买过一次");
        }
        //充足则扣减库存并创建订单返回订单id
        boolean success = seckillVoucherService.update()
                .setSql("stock = stock - 1")
                .eq("voucher_id", voucherVoucherId)
                .gt("stock", 0)
                .update();
        if (!success) {
            //扣减库存
            return Result.fail("库存不足！");
        }
        save(voucherOrder);
        return Result.ok(orderId);

    }


}
