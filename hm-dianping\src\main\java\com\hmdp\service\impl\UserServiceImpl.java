package com.hmdp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.LoginFormDTO;
import com.hmdp.dto.Result;
import com.hmdp.dto.UserDTO;
import com.hmdp.entity.User;
import com.hmdp.mapper.UserMapper;
import com.hmdp.service.IUserService;
import com.hmdp.utils.RedisConstants;
import com.hmdp.utils.RegexPatterns;
import com.hmdp.utils.RegexUtils;
import com.hmdp.utils.SystemConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Result sendCode(String phone, HttpSession session) {
        //校验手机号
        if(RegexUtils.isPhoneInvalid(phone)) {
            //若不符合
            return Result.fail("手机号不合法");
        }
        //如果符合规则，生成验证码
        String code = RandomUtil.randomNumbers(6);
        //保存验证码到session,同时保存发送验证码的手机号
        /*Map<String, String> verifyMap = new HashMap<>();
        verifyMap.put("phone", phone);
        verifyMap.put("code", code);
        session.setAttribute("login:verify", verifyMap);*/

        //保存验证码到redis
        stringRedisTemplate.opsForValue().set(RedisConstants.LOGIN_CODE_KEY + phone, code, RedisConstants.LOGIN_CODE_TTL, TimeUnit.MINUTES);

        //发送验证码
        //这里不做真实发送，模拟日志打印到控制台
        log.info("发送验证码成功，手机号：{}，验证码：{}", phone, code);
        return Result.ok();
    }

    @Override
    public Result userLogin(LoginFormDTO loginForm, HttpSession session) {
        //校验手机号是否合法，是否是和对应验证码有绑定关系
        if(RegexUtils.isPhoneInvalid(loginForm.getPhone())) {
            return Result.fail("手机号不合法");
        }
        /*Map<String, String> verifyMap = (Map<String, String>) session.getAttribute("login:verify");

        if (verifyMap == null
                || !loginForm.getPhone().equals(verifyMap.get("phone"))
                || !loginForm.getCode().equals(verifyMap.get("code"))) {
            return Result.fail("验证码错误或手机号不匹配");
        }*/
        // 从redis获取验证码并校验
        String code = stringRedisTemplate.opsForValue().get(RedisConstants.LOGIN_CODE_KEY + loginForm.getPhone());
        
        // 验证码为空或不匹配
        if(code == null) {
            return Result.fail("手机号不匹配");
        } else if (!code.equals(loginForm.getCode())) {
            log.info("验证码不匹配，输入：{}，预期：{}", loginForm.getCode(), code);
            return Result.fail("验证码错误");
        }
        
        // 验证码正确，删除缓存的验证码
       
        
        //根据手机号查用户是否存在
        User user1 = query().eq("phone", loginForm.getPhone()).one();
        if(user1 == null) {
            user1 = creatUserWithPhone(loginForm.getPhone());
        }

        /*//将用户保存到session
        session.setAttribute("user", BeanUtil.copyProperties(user1, UserDTO.class));*/

        //将用户保存到reids
        //生成一个token作为登陆令牌
        String token = UUID.randomUUID().toString();
        //将user对象转为hash存储
        UserDTO userDTO = BeanUtil.copyProperties(user1, UserDTO.class);
        Map<String, Object> userMap = BeanUtil.beanToMap(userDTO, new HashMap<>(), CopyOptions.create().setIgnoreNullValue(true).setFieldValueEditor((filedName, filedValue) -> filedValue.toString()));
        //存储
        String tokenKey = RedisConstants.LOGIN_USER_KEY + token;
        stringRedisTemplate.opsForHash().putAll(tokenKey, userMap);
        //设置有效期
        stringRedisTemplate.expire(tokenKey, RedisConstants.LOGIN_USER_TTL, TimeUnit.MINUTES);
        //返回token
        return Result.ok(token);
    }

    private User creatUserWithPhone(String phone) {
        User user = new User();
        user.setPhone(phone);
        user.setNickName(SystemConstants.USER_NICK_NAME_PREFIX + RandomUtil.randomString(10));
        save(user);
        return user;
    }
}
